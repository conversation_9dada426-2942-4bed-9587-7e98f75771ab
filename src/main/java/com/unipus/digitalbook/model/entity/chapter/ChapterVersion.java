package com.unipus.digitalbook.model.entity.chapter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.unipus.digitalbook.common.utils.IdentifierUtil;
import com.unipus.digitalbook.model.entity.paper.Paper;
import com.unipus.digitalbook.model.entity.paper.PaperReference;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.params.book.ChapterNodeParam;
import com.unipus.digitalbook.model.po.chapter.ChapterNodePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 章节版本实体类，用于存储章节的版本信息。
 */
@Slf4j
public class ChapterVersion {

    /**
     * 主键ID，唯一标识每个章节版本。
     */
    private Long id;

    /**
     * 章节ID，标识该版本所属的章节。
     */
    private String chapterId;

    /**
     * 版本号，标识章节的具体版本。
     */
    private String versionNumber;

    /**
     * 创建时间，记录章节版本创建的时间戳。
     */
    private Date createTime;

    /**
     * 内容，存储章节版本的具体内容。
     */
    private String content;

    /**
     * 教师内容
     */
    private String studentContent;

    /**
     * 章节名称
     */
    private String name;

    /**
     * 头图
     */
    private String headerImg;

    /**
     * 资源，存储章节版本的资源信息。
     */
    private String resource;

    /**
     * 题列表，存储章节版本的问题列表。
     */
    private List<BigQuestionGroup> questionList;

    /**
     * 试卷引用列表
     */
    private List<PaperReference> paperReferenceList;

    /**
     * 创建者ID，标识创建该章节版本的用户ID。
     */
    private Long createBy;

    /**
     * 创建者名称，标识创建该章节版本的用户名称。
     */
    private String creatorName;

    private List<HeaderNode> headerNodeList;

    private List<ChapterNode> chapterNodeList;

    /**
     * 卷子列表
     */
    private List<Paper> paperList;

    public List<ChapterNode> getChapterNodeTree() {
        if (chapterNodeList == null || chapterNodeList.isEmpty()) {
            return new ArrayList<>();
        }

        // 结果列表，用于存储顶层节点
        List<ChapterNode> result = new ArrayList<>();

        // 用于跟踪当前各级标题的节点
        Map<Integer, ChapterNode> headerStack = new HashMap<>();

        for (ChapterNode node : chapterNodeList) {
            // 创建新节点的深拷贝，避免修改原始列表
            ChapterNode newNode = copyNode(node);

            // 精确匹配h1-h6标题
            if (isHeaderNode(node.getType())) {
                try {
                    // 获取标题级别 (h1->1, h2->2, ...)
                    int level = Integer.parseInt(node.getType().substring(1));

                    // 清理更低级别的标题节点
                    for (int i = level + 1; i <= 6; i++) {
                        headerStack.remove(i);
                    }

                    if (level == 1) {
                        // 顶级标题直接加入结果列表
                        result.add(newNode);
                        headerStack.put(level, newNode);
                    } else {
                        // 子标题加入上一级标题的children
                        ChapterNode parent = null;
                        // 寻找最近的上级标题
                        for (int i = level - 1; i >= 1; i--) {
                            if (headerStack.containsKey(i)) {
                                parent = headerStack.get(i);
                                break;
                            }
                        }

                        if (parent != null) {
                            if (parent.getChildren() == null) {
                                parent.setChildren(new ArrayList<>());
                            }
                            parent.getChildren().add(newNode);
                            headerStack.put(level, newNode);
                        } else {
                            // 如果没有找到父节点（不应该发生），则作为顶级节点
                            result.add(newNode);
                            headerStack.put(level, newNode);
                        }
                    }
                } catch (NumberFormatException e) {
                    // 如果解析标题级别失败，作为内容节点处理
                    addContentNode(newNode, headerStack, result);
                }
            } else {
                // 内容节点处理
                addContentNode(newNode, headerStack, result);
            }
        }

        return result;
    }

    private ChapterVersion() {
        super();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getHeaderImg() {
        return headerImg;
    }

    public void setHeaderImg(String headerImg) {
        this.headerImg = headerImg;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ChapterVersion.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("chapterId='" + chapterId + "'")
                .add("versionNumber='" + versionNumber + "'")
                .add("createTime=" + createTime)
                .add("content='" + content + "'")
                .toString();
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public String getStudentContent() {
        return studentContent;
    }

    public void setStudentContent(String studentContent) {
        this.studentContent = studentContent;
    }

    public List<BigQuestionGroup> getQuestionList() {
        return questionList;
    }

    public void setQuestionList(List<BigQuestionGroup> questionList) {
        this.questionList = questionList;
    }

    public List<PaperReference> getPaperReferenceList() {
        return paperReferenceList;
    }

    public void setPaperReferenceList(List<PaperReference> paperReferenceList) {
        this.paperReferenceList = paperReferenceList;
    }

    public List<HeaderNode> getHeaderNodeList() {
        return headerNodeList;
    }

    public void setHeaderNodeList(List<HeaderNode> headerNodeList) {
        this.headerNodeList = headerNodeList;
    }

    public List<ChapterNode> getChapterNodeList() {
        return chapterNodeList;
    }

    public void setChapterNodeList(List<ChapterNode> chapterNodeList) {
        this.chapterNodeList = chapterNodeList;
    }

    public static class Builder {
        private Long id;
        private String chapterId;
        private String versionNumber;
        private Date createTime;
        private String content;

        private String resources;

        private String studentContent;

        private String name;

        private String headerImg;

        private List<HeaderNode> headerNodeList;

        private List<BigQuestionGroup> questionList;

        private List<PaperReference> paperReferenceList;

        private List<ChapterNode> chapterNodeList;

        private Long createBy;

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder chapterId(String chapterId) {
            this.chapterId = chapterId;
            return this;
        }

        public Builder versionNumber() {
            this.versionNumber = IdentifierUtil.generateVersion();
            return this;
        }
        public Builder versionNumber(String versionNumber) {
            this.versionNumber = versionNumber;
            return this;
        }

        public Builder createTime(Date createTime) {
            this.createTime = createTime;
            return this;
        }

        public Builder content(String content) {
            this.content = content;
            return this;
        }

        public Builder createBy(Long createBy) {
            this.createBy = createBy;
            return this;
        }

        public Builder resources(String resources) {
            this.resources = resources;
            return this;
        }

        public Builder studentContent(String studentContent) {
            this.studentContent = studentContent;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder headerImg(String headerImg) {
            this.headerImg = headerImg;
            return this;
        }

        public Builder catalog(String catalog) {
            this.headerNodeList = parseCatalog(catalog);
            return this;
        }

        public Builder chapterNodeParamList(List<ChapterNodeParam> chapterNodeList) {
            if (CollectionUtils.isEmpty(chapterNodeList)) {
                return this;
            }
            this.chapterNodeList = chapterNodeList.stream().map(ChapterNodeParam::toEntity).toList();
            return this;
        }

        public Builder chapterNodePOList(List<ChapterNodePO> chapterNodeList) {
            if (CollectionUtils.isEmpty(chapterNodeList)) {
                return this;
            }
            this.chapterNodeList = chapterNodeList.stream().map(ChapterNodePO::toEntity).toList();
            return this;
        }


        private List<HeaderNode> parseCatalog(String catalog) {
            List<HeaderNode> headerNodeResList = new ArrayList<>();
            if (StringUtils.isBlank(catalog)) {
                log.debug("目录为空，返回空列表");
                return headerNodeResList;
            }

            try {
                return parseAndValidateCatalog(catalog);
            } catch (JSONException e) {
                log.error("解析目录JSON格式错误: {}", e.getMessage(), e);
            } catch (Exception e) {
                log.error("解析目录时发生未知异常: {}", e.getMessage(), e);
            }

            return headerNodeResList;
        }

        private List<HeaderNode> parseAndValidateCatalog(String catalog) {
            // 解析JSON字符串到HeaderNode列表
            List<HeaderNode> parsedNodes = JSON.parseArray(catalog, HeaderNode.class);
            if (parsedNodes == null) {
                return new ArrayList<>();
            }

            // 过滤掉无效的HeaderNode
            List<HeaderNode> validNodes = new ArrayList<>();
            for (HeaderNode node : parsedNodes) {
                if (isValidHeaderNode(node)) {
                    validNodes.add(node);
                } else {
                    log.warn("忽略无效的目录节点: {}\n 原始数据：{}", node, catalog);
                }
            }

            return validNodes;
        }

        /**
         * 验证HeaderNode对象是否有效
         * @param node 待验证的HeaderNode对象
         * @return 是否有效
         */
        private boolean isValidHeaderNode(HeaderNode node) {
            if (node == null) {
                return false;
            }
            // 验证必要字段是否为空
            if (StringUtils.isBlank(node.getId()) ||
                    StringUtils.isBlank(node.getType())) {
                return false;
            }
            // 验证标题类型是否符合h1-h6格式
            String nodeType = node.getType().toLowerCase();
            if (!nodeType.matches("h[1-6]")) {
                return false;
            }
            // key值验证 - 确保为正整数
            if (node.getKey() <= 0) {
                return false;
            }

            // 其他可能的业务逻辑验证...
            if (StringUtils.isBlank(node.getText())) {
                return false;
            }
//            log.debug("验证通过: {}", node);
            return true;
        }

        public Builder questionList(List<BigQuestionGroup> questionList) {
            this.questionList = questionList;
            return this;
        }

        public Builder paperReferenceList(List<PaperReference> paperReferenceList) {
            this.paperReferenceList = paperReferenceList;
            return this;
        }

        public ChapterVersion build() {
            if (chapterId == null) {
                throw new IllegalArgumentException("chapterId is null");
            }
            if (versionNumber == null) {
                versionNumber = IdentifierUtil.generateVersion();
            }
            if (chapterNodeList == null) {
                chapterNodeList = new ArrayList<>();
            }
            return buildChapterVersion();
        }

        private ChapterVersion buildChapterVersion() {
            ChapterVersion chapterVersion = new ChapterVersion();
            chapterVersion.setId(id);
            chapterVersion.setChapterId(chapterId);
            chapterVersion.setVersionNumber(versionNumber);
            chapterVersion.setCreateTime(createTime);
            chapterVersion.setContent(content);
            chapterVersion.setCreateBy(createBy);
            chapterVersion.setResource(resources);
            chapterVersion.setStudentContent(studentContent);
            chapterVersion.setName(name);
            chapterVersion.setHeaderImg(headerImg);
            chapterVersion.setHeaderNodeList(headerNodeList);
            chapterVersion.setQuestionList(questionList);
            chapterVersion.setPaperReferenceList(paperReferenceList);
            chapterVersion.setChapterNodeList(chapterNodeList);
            return chapterVersion;
        }

        public static Builder getInstance() {
            return new Builder();
        }
    }


    // 创建节点的深拷贝，避免修改原始列表
    private ChapterNode copyNode(ChapterNode original) {
        ChapterNode copy = new ChapterNode();
        copy.setId(original.getId());
        copy.setText(original.getText());
        copy.setType(original.getType());
        copy.setWordCount(original.getWordCount());
        copy.setAudioDuration(original.getAudioDuration());
        copy.setVideoDuration(original.getVideoDuration());
        copy.setQuestionType(original.getQuestionType());
        // 不复制children，会在构建树结构时添加
        return copy;
    }
    // 辅助方法：添加内容节点
    private void addContentNode(ChapterNode node, Map<Integer, ChapterNode> headerStack, List<ChapterNode> result) {
        // 尝试找到最近的标题节点作为父节点
        ChapterNode parent = null;
        for (int i = 6; i >= 1; i--) {
            if (headerStack.containsKey(i)) {
                parent = headerStack.get(i);
                break;
            }
        }

        if (parent != null) {
            // 如果找到父节点，将内容节点添加到其子节点列表
            if (parent.getChildren() == null) {
                parent.setChildren(new ArrayList<>());
            }
            parent.getChildren().add(node);
        } else {
            // 如果没有找到父节点，将内容节点添加到顶级列表
            result.add(node);
        }
    }

    // 检查是否为h1-h6标题节点
    private boolean isHeaderNode(String type) {
        return type != null &&
                (type.equals("h1") || type.equals("h2") || type.equals("h3") ||
                        type.equals("h4") || type.equals("h5") || type.equals("h6"));
    }

    public List<Paper> getPaperList() {
        return paperList;
    }

    public void setPaperList(List<Paper> paperList) {
        this.paperList = paperList;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
