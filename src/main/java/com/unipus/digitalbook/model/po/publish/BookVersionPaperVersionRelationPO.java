package com.unipus.digitalbook.model.po.publish;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * @TableName book_version_paper_version_relation
 */
public class BookVersionPaperVersionRelationPO implements Serializable {
    /**
     * 关系ID
     */
    private Long id;

    /**
     * 教材版本ID
     */
    private Long bookVersionId;

    /**
     * 试卷版本ID
     */
    private Long paperVersionId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 最后更新者ID
     */
    private Long updateBy;

    /**
     * 是否有效 0-无效 1-有效
     */
    private Boolean enable;

    @Serial
    private static final long serialVersionUID = 1L;

    public BookVersionPaperVersionRelationPO() {}

    public BookVersionPaperVersionRelationPO(Long bookVersionId, Long paperVersionId, Long userId) {
        this.bookVersionId = bookVersionId;
        this.paperVersionId = paperVersionId;
        this.createBy = userId;
        this.updateBy = userId;
    }

    /**
     * 关系ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 关系ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 教材版本ID
     */
    public Long getBookVersionId() {
        return bookVersionId;
    }

    /**
     * 教材版本ID
     */
    public void setBookVersionId(Long bookVersionId) {
        this.bookVersionId = bookVersionId;
    }

    /**
     * 试卷版本ID
     */
    public Long getPaperVersionId() {
        return paperVersionId;
    }

    /**
     * 试卷版本ID
     */
    public void setPaperVersionId(Long paperVersionId) {
        this.paperVersionId = paperVersionId;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 创建者ID
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * 创建者ID
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * 最后更新者ID
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * 最后更新者ID
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public Boolean getEnable() {
        return enable;
    }

    /**
     * 是否有效 0-无效 1-有效
     */
    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}